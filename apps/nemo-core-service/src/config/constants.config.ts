import { HttpStatus } from '@nestjs/common';

export const BASE_COLLATE = 'COLLATE "th-TH-x-icu"';
export const CACHE_KEY_ALL = 'CACHE_ALL';

export interface BaseExceptionPayload {
  code: number;
  status: HttpStatus;
  message: string;

  commonException?: CommonExceptionPayload;
}

export interface CommonExceptionPayload {
  th: {
    title: string;
    description: string;
  };
  en: {
    title: string;
    description: string;
  };
}

export interface BaseException {
  [key: string]: BaseExceptionPayload;
}

export const BASE_EXCEPTIONS = {
  NOT_FOUND_DATA: {
    code: '2000',
    status: HttpStatus.NOT_FOUND,
    message: 'Not found data',
  },
  NOT_FOUND_USER: {
    code: '2001',
    status: HttpStatus.NOT_FOUND,
    message: 'User not found',
  },
  UNAUTHORIZED: {
    code: '3000',
    status: HttpStatus.UNAUTHORIZED,
    message: 'Unauthorized',
  },
  COMPANY_INVALID: {
    code: '4000',
    status: HttpStatus.BAD_REQUEST,
    message: 'Company Invalid',
  },
  BRANCH_INVALID: {
    code: '4001',
    status: HttpStatus.FORBIDDEN,
    message: 'Branch Invalid',
  },
  BODY_PAYLOAD_INVALID: {
    code: '4002',
    status: HttpStatus.BAD_REQUEST,
    message: 'Body payload invalid',
  },
  ROLES_INVALID: {
    code: '4003',
    status: HttpStatus.FORBIDDEN,
    message: 'Roles invalid in branch',
  },
  USER_TYPE_INVALID: {
    code: '4004',
    status: HttpStatus.FORBIDDEN,
    message: 'User type is invalid',
  },
  INTERNAL_SERVER_ERROR: {
    code: '5000',
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Internal Server Error',
  },
  INVALID_MODEL_IDENTIFIERS: {
    code: '101001',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid model identifiers. Model master not found',
  },
  INVALID_INPUT_FOR_UPDATE_JOB: {
    code: '101002',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid input for update job',
  },
  UNAVAILABLE_JOB: {
    code: '101003',
    status: HttpStatus.BAD_REQUEST,
    message: 'Requested job is canceled',
  },
  INVALID_JOB_PERMISSION: {
    code: '101004',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid job permission',
  },
  JOB_EMAIL_ERROR: {
    code: '101005',
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Job Email Error',
  },
  INVALID_JOB_FOR_ACTION: {
    code: '101006',
    status: HttpStatus.BAD_REQUEST,
    message: 'Job data not match condition of current action',
  },
  UNAVAILABLE_ESTIMATION_ACTIVITY: {
    code: '101007',
    status: HttpStatus.BAD_REQUEST,
    message: 'Estimation activity is already assigned to job',
  },
  EXPIRED_ESTIMATION_ACTIVITY: {
    code: '101008',
    status: HttpStatus.BAD_REQUEST,
    message: 'Estimation activity is expired',
  },
  INVALID_ESTIMATION_ACTIVITY: {
    code: '101009',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid estimation activity',
  },
  INVALID_JOB_RELATION: {
    code: '101010',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid job relation',
  },
  IMEI_NOT_SOLD: {
    code: '101011',
    status: HttpStatus.BAD_REQUEST,
    message: 'Device with this imei is not sold',
  },
  REQUIRED_FIELD_INCOMPLETE: {
    code: '102001',
    status: HttpStatus.BAD_REQUEST,
    message: 'Required field incomplete',
  },
  INVALID_DATA_TYPE: {
    code: '102002',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid data type',
  },
  INVALID_DATA_FORMAT: {
    code: '102003',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid data format',
  },
  INVALID_RANGE: {
    code: '102004',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid range',
  },
  INVALID_FILE_TYPE: {
    code: '102005',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid file type',
  },
  INVALID_FILE_SIZE: {
    code: '102006',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid file size',
  },
  INVALID_SHEET_NAME: {
    code: '102007',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid excel sheet name',
  },
  DUPLICATE_DATA_RECORD: {
    code: '102008',
    status: HttpStatus.BAD_REQUEST,
    message: 'Duplicate record in excel sheet',
  },
  KEY_NOT_EXIST: {
    code: '102009',
    status: HttpStatus.BAD_REQUEST,
    message: 'Specified key(s) does not exist',
  },
  DATA_ROW_EXCEED_LIMIT: {
    code: '102010',
    status: HttpStatus.BAD_REQUEST,
    message: 'Data row exceed limit',
  },
  INVALID_INPUT_FOR_UPDATE_VOUCHER: {
    code: '103001',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid input for update voucher',
  },
  VOUCHER_ALREADY_USED: {
    code: '103002',
    status: HttpStatus.BAD_REQUEST,
    message: 'Voucher is already used',
  },
  INVALID_INPUT_FOR_GET_VOUCHER: {
    code: '103003',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid input for get voucher',
  },
  REDEMPTION_FAILED: {
    code: '103004',
    status: HttpStatus.BAD_REQUEST,
    message: 'Redemption failed',
  },
  VOUCHER_NOT_FOUND: {
    code: '103005',
    status: HttpStatus.BAD_REQUEST,
    message: 'Voucher not found',
  },
  EXCEED_MAX_RETRY_FOR_FINDING_VOUCHER: {
    code: '103006',
    status: HttpStatus.BAD_REQUEST,
    message: 'Exceed max retry for finding voucher ',
  },
  VOUCHER_ALREADY_UPLOADED: {
    code: '103007',
    status: HttpStatus.BAD_REQUEST,
    message: 'Voucher already uploaded',
  },
  VOUCHER_IS_PROCESSING: {
    code: '103008',
    status: HttpStatus.BAD_REQUEST,
    message: 'The voucher is processing',
  },
  JOB_ALREADY_PACKED: {
    code: '104001',
    status: HttpStatus.BAD_REQUEST,
    message: 'Job already packed for do',
  },
  INVALID_DO_STATUS_TO_CREATE: {
    code: '104002',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid status for create do',
  },
  INVALID_DO_STATUS_TO_UPDATE: {
    code: '104003',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid status for update do',
  },
  INVALID_DO_STATUS_TO_CONFIRM: {
    code: '104004',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid status for confirm do',
  },
  DO_ALREADY_CHANGED: {
    code: '104005',
    status: HttpStatus.BAD_REQUEST,
    message: 'DO already changed',
  },
  INVALID_INPUT_FOR_UPDATE_DO: {
    code: '104006',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid input for update do',
  },
  INVALID_UPDATE_JOB_ALREADY_PACKED_ANOTHER_DO: {
    code: '104007',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid job already packed for update do',
  },
  INVALID_SENDER_ROLE_IN_BRANCH: {
    code: '104008',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid sender role in branch',
  },
  JOB_ALREADY_CHANGED: {
    code: '105003',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid job to create allocation order',
  },
  INVALID_AO_STATUS_TO_UPDATE: {
    code: '105004',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid status for update ao',
  },
  INVALID_AO_STATUS_TO_CONFIRM: {
    code: '105005',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid status for confirm ao',
  },
  INVALID_AO_OWNER: {
    code: '105007',
    status: HttpStatus.FORBIDDEN,
    message: 'Unauthorized for update ao',
  },
  INVALID_AO_FOR_ACTION: {
    code: '105008',
    status: HttpStatus.BAD_REQUEST,
    message: 'AO data not match condition of current action',
  },
  INVALID_ALLOCATION_DRAFT: {
    code: '105009',
    status: HttpStatus.BAD_REQUEST,
    message: 'Insert error during create draft',
  },
  DUPLICATED_JOB: {
    code: '105010',
    status: HttpStatus.BAD_REQUEST,
    message: 'Duplicated job input',
  },
  JOB_LIST_REQUIRED: {
    code: '105011',
    status: HttpStatus.BAD_REQUEST,
    message: 'JobList Required',
  },
  INVALID_JOB_ID_INPUT: {
    code: '105012',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid jobId from jobList',
  },
  INVALID_BRANCH_TYPE: {
    code: '105013',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid branchType',
  },
  INVALID_INPUT_FOR_UPDATE_AO: {
    code: '105014',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid input for update ao',
  },
  CAMPAIGN_NOT_FOUND: {
    code: '106001',
    status: HttpStatus.NOT_FOUND,
    message: 'Campaign not found',
  },
  CAMPAIGN_REDEMPTION_CODE_NOT_FOUND: {
    code: '106002',
    status: HttpStatus.NOT_FOUND,
    message: 'Campaign redemption code not found',
  },
  CAMPAIGN_NOT_EXISTED: {
    code: '106003',
    status: HttpStatus.BAD_REQUEST,
    message: 'Campaign not existed',
  },
  MODEL_KEY_NOT_EXISTED: {
    code: '106004',
    status: HttpStatus.BAD_REQUEST,
    message: 'Model key not existed',
  },
  REDEMPTION_CODE_ALREADY_UPLOADED: {
    code: '106005',
    status: HttpStatus.BAD_REQUEST,
    message: 'Redemption code already uploaded',
  },
  CAMPAIGN_NOT_ACTIVE: {
    code: '106006',
    status: HttpStatus.BAD_REQUEST,
    message: 'Campaign not active or expired',
  },
  VOUCHER_ORDER_IS_OCCUPIED: {
    code: '106007',
    status: HttpStatus.BAD_REQUEST,
    message: 'Voucher order is occupied',
  },
  INVALID_ISSUE_STATUS_TO_UPDATE: {
    code: '107001',
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid status for update',
  },
  ROLE_NAME_ALREADY_EXISTS: {
    code: '108001',
    status: HttpStatus.BAD_REQUEST,
    message: 'Role name already exists',
  },
  ROLE_NOT_EXISTS: {
    code: '110001',
    status: HttpStatus.NOT_FOUND,
    message: 'Role not exists',
  },
  BRANCH_NOT_EXISTS: {
    code: '110002',
    status: HttpStatus.NOT_FOUND,
    message: 'Branch not exists',
  },
};

export const COMMON_EXCEPTIONS_TITLE_DESCRIPTION = {
  COMMON_ERROR: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'กรุณาทำรายการใหม่อีกครั้ง',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Please try again later.',
    },
  },
  COMMON_ZOD_ERROR: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'ชนิดของข้อมูลในคำขอไม่ถูกต้อง',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Data type in request was invalid.',
    },
  },
  DEVICE_SYSTEM_CODE_NOT_FOUND: {
    th: {
      title: 'ไม่สามารถรับฝากอุปกรณ์ของคุณได้',
      description: 'อุปกรณ์ของคุณไม่เข้าข่ายรายการรับฝากของบริษัท',
    },
    en: {
      title: 'Unable to purchase your device.',
      description: 'Your device does not qualify for purchase list.',
    },
  },
  APPRAISAL_SKU_NOT_FOUND: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่พบข้อมูล อุปกรณ์ดังกล่าว',
    },
    en: {
      title: 'Something went wrong.',
      description: 'No information found for the specified device.',
    },
  },
  ORDER_SKU_NOT_FOUND: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'สินค้าไม่เข้าเงื่อนไขการรับฝาก',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Product has not found.',
    },
  },
  ORDER_BODY_INCOMPLETE: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'กรุณากรอกข้อมูลให้ครบถ้วน',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Please complete the information.',
    },
  },
  ORDER_REQUIRED_CHECKLIST_INCOMPLETE: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'กรุณากรอกข้อมูลให้ครบถ้วน',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Please complete the information.',
    },
  },
  ORDER_MODULE_NOT_FOUND: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description:
        'ไม่สามารถดำเนินการได้ โปรดออกจากระบบ และเริ่มต้นทำรายการใหม่อีกครั้ง',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Unable to proceed. Please create new order.',
    },
  },
  ORDER_MODULE_STATUS_INVALID: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description:
        'ไม่สามารถดำเนินการได้ โปรดออกจากระบบ และเริ่มต้นทำรายการใหม่อีกครั้ง',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Unable to proceed. Please create new order.',
    },
  },
  ORDER_QUESTION_NOT_FOUND: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description:
        'ไม่สามารถดำเนินการได้ โปรดออกจากระบบ และเริ่มต้นทำรายการใหม่อีกครั้ง',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Unable to proceed. Please create new order.',
    },
  },
  ORDER_SELECTION_ANS_INVALID: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'เลือกคำตอบได้เพียง 1 ข้อเท่านั้น',
    },
    en: {
      title: 'Something went wrong.',
      description: 'You can only choose one answer.',
    },
  },
  ORDER_ANSWER_INVALID: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'คำตอบไม่ถูกต้อง',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Answer is not match.',
    },
  },
  ORDER_SKIP_INVALID: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'กรุณากรอกข้อมูลให้ครบถ้วน',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Please complete the information.',
    },
  },
  ORDER_MODEL_COLOR_INVALID: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่พบสีดังกล่าว',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Color not found.',
    },
  },
  ORDER_STORE_ID_NOT_FOUND: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่พบสาขาดังกล่าว',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Branch not found.',
    },
  },
  ORDER_ACCEPT_PDPA_REQUIRED: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'กรุณายอมรับข้อกำหนดและเงื่อนไข',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Please accept term and condition.',
    },
  },
  ORDER_PDPA_VERSION_REQUIRED: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'กรุณากรอกข้อมูลให้ครบถ้วน',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Please complete the information.',
    },
  },
  ORDER_PDPA_VERSION_NOT_FOUND: {
    th: {
      title: 'เวอร์ชันเอกสารข้อกำหนดและเงื่อนไขไม่ถูกต้อง',
      description: 'กรุณาแก้ไขข้อมูลให้เป็นปัจจุบัน',
    },
    en: {
      title: 'Term and condition version is not existed.',
      description: 'Please update term and condition information.',
    },
  },
  ORDER_CUSTOMER_INFO_INVALID: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'กรุณาระบุข้อมูลให้ถูกต้อง',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Please provide accurate information.',
    },
  },
  ORDER_ID_NOT_FOUND: {
    th: {
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่พบรายการในระบบ',
    },
    en: {
      title: 'Something went wrong.',
      description: 'Order not found.',
    },
  },
};

export const COMMON_EXCEPTIONS = {
  NOT_FOUND_DATA: {
    code: '2000',
    status: HttpStatus.NOT_FOUND,
    message: 'Not found data',
    commonException: COMMON_EXCEPTIONS_TITLE_DESCRIPTION.COMMON_ERROR,
  },
  UNAUTHORIZED: {
    code: '3000',
    status: HttpStatus.UNAUTHORIZED,
    message: 'Unauthorized',
    commonException: COMMON_EXCEPTIONS_TITLE_DESCRIPTION.COMMON_ERROR,
  },
  COMPANY_INVALID: {
    code: '4000',
    status: HttpStatus.BAD_REQUEST,
    message: 'Company Invalid',
    commonException: COMMON_EXCEPTIONS_TITLE_DESCRIPTION.COMMON_ERROR,
  },
  BODY_PAYLOAD_INVALID: {
    code: '4002',
    status: HttpStatus.BAD_REQUEST,
    message: 'Body payload invalid',
  },
};

// ** Mapping url in x-company request headers with company id **
const urlWithCompanyId = {
  WW: 'WW',
  'ww-remobile-dev.axonstech.com': 'WW',
  'ww-remobile-uat.axonstech.com': 'WW',
  'ww-remobile.axonstech.com': 'WW',
  'ww-remobile-cms-dev.axonstech.com': 'WW',
  'ww-remobile-cms-uat.axonstech.com': 'WW',
  'ww-remobile-cms.axonstech.com': 'WW',
};

export const getS3AllocationOrdersUrlPath = (
  company: string,
  aoId: string,
  key: string,
): string => {
  return `company/${company}/allocation-orders/${aoId}/video/${key}`;
};

export const getS3JobUrlPath = (
  company: string,
  jobId: string,
  key: string,
): string => {
  return `company/${company}/jobs/${jobId}/media/${key}`;
};
// ** Mapping url with key from x-company request headers with company id **
export const mappingUrlWithCompanyId = (url: string): string | null => {
  // Get company id
  const companyId = urlWithCompanyId[url as keyof typeof urlWithCompanyId];

  // Prevent url invalid
  if (!companyId) {
    return null;
  }

  // Return company id
  return companyId;
};

// Declare encrypt key
export const encryptKeyList = ['firebase', 'ms', 'ocr_service_account'];

export const getPermssionAll = (
  permssionAction: PermissionAction,
  filterRole?: FilterRole,
) => {
  const array: string[] = [];
  if (filterRole === FilterRole.CMS) {
    for (const [key, value] of Object.entries(Permission)) {
      if (key.startsWith('CMS_')) {
        array.push(value + permssionAction);
      }
    }
    return array;
  } else if (filterRole === FilterRole.SHOP) {
    for (const [key, value] of Object.entries(Permission)) {
      if (key.startsWith('SHOP_')) {
        array.push(value + permssionAction);
      }
    }
    return array;
  } else {
    for (const [, value] of Object.entries(Permission)) {
      array.push(value + permssionAction);
    }
    return array;
  }
};

export enum FilterRole {
  CMS = 'CMS',
  SHOP = 'SHOP',
}

export enum Role {
  MANAGER = 'Manager',
  SALE = 'Sale',
  SUPER_ADMIN = 'SuperAdmin',
  ADMIN = 'Admin',
  PRICE_ESTIMATOR = 'AdminPriceEstimator',
  RECEIVE = 'AdminReceive',
  QC = 'AdminQC',
  REPAIR = 'AdminRepair',
  INSPECTION = 'AdminInspection',
  SUPPLY_CHAIN = 'AdminSupplyChain',
  PRODUCT = 'AdminProduct',
  MARKETING = 'AdminMarketing',
  RCC = 'AdminRCC',
}

export enum Permission {
  CMS_ROLE_MANAGE = 'PS-0001',
  CMS_STAFF_MANAGE = 'PS-0002',
  CMS_REPORT = 'PS-0003',
  CMS_JOB_ALL = 'PS-0004',
  CMS_JOB_MY = 'PS-0005',
  CMS_DELIVERY_ORDER_BY_DO = 'PS-0006',
  CMS_DELIVERY_ORDER_BY_JOB = 'PS-0007',
  CMS_QC_ALL = 'PS-0008',
  CMS_QC_MY = 'PS-0009',
  CMS_REPAIR_PENDING = 'PS-0010',
  CMS_REPAIR_MY = 'PS-0011',
  CMS_REPAIR_COMPLETE = 'PS-0012',
  CMS_INSPECTION_ALL = 'PS-0013',
  CMS_INSPECTION_MY = 'PS-0014',
  CMS_PRICE_CONFIRM_ALL = 'PS-0015',
  CMS_ALLOCATION_ORDER_ALL = 'PS-0016',
  CMS_OPERATION_COST = 'PS-0017',
  CMS_VOUCHER_MANAGE = 'PS-0018',
  CMS_CAMPAIGN_MANAGE = 'PS-0019',
  CMS_MODEL_MANAGE = 'PS-0020',
  CMS_MODEL_COST_MANAGE = 'PS-0021',
  CMS_BRANCH_MANAGE = 'PS-0022',
  CMS_ISSUE_REPORT = 'PS-0023',
  SHOP_JOB_MY = 'PS-0024',
  SHOP_JOB_HX = 'PS-0025',
  SHOP_JOB_TEAM = 'PS-0026',
  SHOP_DELIVERY_ORDER_ALL = 'PS-0027',
  SHOP_ALLOCATION_ORDER_ALL = 'PS-0028',
  SHOP_ISSUE_REPORT = 'PS-0029',
  SHOP_OTHERS_OCR_CONFIRM = 'PS-0030',
  CMS_SAP_REPORT = 'PS-0031',
  CMS_TRANSFER_VOUCHER = 'PS-0032',
}
export enum PermissionAction {
  VIEW = '_VIEW',
  CREATE = '_CREATE',
  UPDATE = '_UPDATE',
  DELETE = '_DELETE',
  DOWNLOAD = '_DOWNLOAD',
  UPLOAD = '_UPLOAD',
}
// Job Get id
export const permissionCMSGetJobId = [
  Permission.CMS_JOB_ALL + PermissionAction.VIEW,
  Permission.CMS_JOB_MY + PermissionAction.VIEW,
  Permission.CMS_DELIVERY_ORDER_BY_DO + PermissionAction.VIEW,
  Permission.CMS_DELIVERY_ORDER_BY_JOB + PermissionAction.VIEW,
  Permission.CMS_QC_ALL + PermissionAction.VIEW,
  Permission.CMS_QC_MY + PermissionAction.VIEW,
  Permission.CMS_REPAIR_PENDING + PermissionAction.VIEW,
  Permission.CMS_REPAIR_MY + PermissionAction.VIEW,
  Permission.CMS_REPAIR_COMPLETE + PermissionAction.VIEW,
  Permission.CMS_INSPECTION_ALL + PermissionAction.VIEW,
  Permission.CMS_INSPECTION_MY + PermissionAction.VIEW,
  Permission.CMS_PRICE_CONFIRM_ALL + PermissionAction.VIEW,
  Permission.CMS_ALLOCATION_ORDER_ALL + PermissionAction.VIEW,
];
// Job Get count and menu count
export const permissionCMSGetCountAndMenuCount = [
  Permission.CMS_JOB_ALL + PermissionAction.VIEW,
  Permission.CMS_JOB_MY + PermissionAction.VIEW,
  Permission.CMS_REPAIR_PENDING + PermissionAction.VIEW,
  Permission.CMS_REPAIR_MY + PermissionAction.VIEW,
  Permission.CMS_INSPECTION_ALL + PermissionAction.VIEW,
  Permission.CMS_INSPECTION_MY + PermissionAction.VIEW,
  Permission.CMS_PRICE_CONFIRM_ALL + PermissionAction.VIEW,
  Permission.CMS_OPERATION_COST + PermissionAction.VIEW,
];

// Job Get list
export const permissionCMSGetJobList = [
  Permission.CMS_REPORT + PermissionAction.VIEW,
  ...permissionCMSGetJobId,
];

// Branch Get list
export const permissionCMSGetBranch = [
  Permission.CMS_STAFF_MANAGE + PermissionAction.VIEW,
  ...permissionCMSGetJobList,
  Permission.CMS_BRANCH_MANAGE + PermissionAction.VIEW,
];

export enum TypeChecklistFunctionSection {
  PRODUCT_INFORMATION = 'product_information',
  REMOBIE_CHECK_LIST = 'remobie_check_list',
}
