import { MigrationInterface, QueryRunner } from "typeorm";

export class RenameJobVendorToVendorTypeInJob1753072802000 implements MigrationInterface {
    name = 'RenameJobVendorToVendorTypeInJob1753072802000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Rename column jobVendor to vendorType in job table if exists
        await queryRunner.query(`ALTER TABLE "core"."job" RENAME COLUMN "job_vendor" TO "vendor_type"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert column name from vendorType back to jobVendor
        await queryRunner.query(`ALTER TABLE "core"."job" RENAME COLUMN "vendor_type" TO "job_vendor"`);
    }
}
