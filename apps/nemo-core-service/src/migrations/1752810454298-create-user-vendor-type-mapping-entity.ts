import { MigrationInterface, QueryRunner } from "typeorm";
import { UserVendorTypeMappingEntity } from "../entities";

export class CreateUserVendorTypeMappingEntity1752810454298 implements MigrationInterface {
    name = 'CreateUserVendorTypeMappingEntity1752810454298'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create user_vendor_type_mapping table
        await queryRunner.query(`CREATE TABLE "core"."user_vendor_type_mapping" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "user_type" character varying(30) NOT NULL, "vendor_type" character varying(30) NOT NULL, CONSTRAINT "PK_user_vendor_type_mapping" PRIMARY KEY ("user_type", "vendor_type"))`);

        // Insert seed data
        const seedData = [
            { userType: 'WW', vendorType: 'MASS' },
            { userType: 'KINGFISHER', vendorType: 'KINGFISHER' }
        ];

        for (const data of seedData) {
            const userVendorMapping = new UserVendorTypeMappingEntity();
            userVendorMapping.userType = data.userType;
            userVendorMapping.vendorType = data.vendorType;
            await queryRunner.manager.save(userVendorMapping);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the table
        await queryRunner.query(`DROP TABLE "core"."user_vendor_type_mapping"`);
    }
}
