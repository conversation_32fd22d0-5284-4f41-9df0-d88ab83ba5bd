import {
  Controller,
  Get,
  Query,
  Req,
  UploadedFile,
  UseInterceptors,
  Put,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FileInterceptor } from '@nestjs/platform-express';
import { CrudController } from '../../crud';
import { BranchEntity, branchType } from '../../entities';
import { Repository, SelectQueryBuilder, EntityManager } from 'typeorm';
import { Request } from 'express';
import { BranchesService } from './branches.service';
import { GetBranchesDto } from './dto';
import {
  mappingUrlWithCompanyId,
  permissionCMSGetBranch,
  Permission,
  PermissionAction,
} from '../../config';
import { Permissions } from '../../decorators';

@Controller('v1/admin/branches')
export class BranchesController extends CrudController<BranchEntity> {
  constructor(
    @InjectRepository(BranchEntity) repo: Repository<BranchEntity>,
    private readonly branchesService: BranchesService,
  ) {
    super(BranchEntity, 'branch', repo, {
      resourceKeyPath: 'branchId',
      order: { updatedAt: 'asc' },
      defaultFilter: async (
        request: Request,
        listQuery: SelectQueryBuilder<BranchEntity>,
      ) => {
        const xCompany = request.headers['x-company'] as string;

        const company = mappingUrlWithCompanyId(xCompany);

        return listQuery.andWhere(
          `(r.companyId = :company and r.branchType != :type)`,
          {
            company: company,
            type: branchType.ADMIN,
          },
        );
      },
      searchFilter: async (
        request: Request,
        _em: EntityManager,
        listQuery: SelectQueryBuilder<BranchEntity>,
      ) => this.branchesService.buildSearchQuery(request, listQuery),
      afterLoad: [
        async (context, data, isMany): Promise<any[]> =>
          isMany ? this.branchesService.afterLoad(context, data) : data,
      ],
    });
  }

  @Get()
  @Permissions(permissionCMSGetBranch)
  async getBranches(@Req() context: Request, @Query() query: GetBranchesDto) {
    const orderBy = query.orderBy;
    if (orderBy) {
      query.orderBy = query.orderBy + ',branchId asc';
    }
    console.log('query', query);
    const result = await super.findAll(context, {
      ...query,
    });
    return result;
  }

  @Put()
  @Permissions([Permission.CMS_BRANCH_MANAGE + PermissionAction.UPLOAD])
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @Req() req: Request,
    @UploadedFile() file: Express.Multer.File,
  ) {
    const xCompany = req.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);

    return this.branchesService.putBranches(
      company ? company : undefined,
      file,
    );
  }

  @Get('/export')
  @Permissions([Permission.CMS_BRANCH_MANAGE + PermissionAction.DOWNLOAD])
  async getBranchesFile(
    @Req() context: Request,
    @Query() query: GetBranchesDto,
  ) {
    const xCompany = context.headers['x-company'] as string;
    const company = mappingUrlWithCompanyId(xCompany);

    query.companyId = company || undefined;
    query.orderBy = 'updatedAt desc';
    query.pagination = 'false';
    const result = await super.findAll(context, {
      ...query,
    });
    const buf = await this.branchesService.exportBranches(result.items);

    return {
      base64String: buf.toString('base64'),
    };
  }
}
