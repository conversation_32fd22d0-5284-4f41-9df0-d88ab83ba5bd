export const newDeviceDetailSurvey = (language: string, modelKey: any) => ({
  logoPosition: 'right',
  pages: [
    {
      name: 'page1',
      elements: [
        {
          type: 'dropdown',
          name: 'brand',
          title: 'ยี่ห้อ',
          hideNumber: true,
          isRequired: true,
          requiredErrorText: 'กรุณาเลือกยี่ห้อ',
          placeholder: 'กรุณาเลือกยี่ห้อ',
          readOnly: true,
          choicesByUrl: {
            url: `${process.env.NEXT_PUBLIC_NEMO_SERVICE_HOST}/api/core/v1/shop/model-masters/lov/brand`,
            valueName: 'label',
            path: 'data',
          },
        },
        {
          type: 'dropdown',
          name: 'model',
          startWithNewLine: false,
          title: 'รุ่น',
          hideNumber: true,
          isRequired: true,
          placeholder: 'กรุณาเลือกรุ่น',
          readOnly: true,
          choicesByUrl: {
            url: `${process.env.NEXT_PUBLIC_NEMO_SERVICE_HOST}/api/core/v1/shop/model-masters/lov/model?brand={brand}`,
            valueName: 'label',
            path: 'data',
          },
        },
        {
          type: 'dropdown',
          name: 'rom',
          startWithNewLine: false,
          title: 'ความจุ',
          hideNumber: true,
          isRequired: true,
          placeholder: 'กรุณาเลือกความจุ',
          readOnly: true,
          choicesByUrl: {
            url: `${process.env.NEXT_PUBLIC_NEMO_SERVICE_HOST}/api/core/v1/shop/model-masters/lov/rom?brand={brand}&model={model}`,
            valueName: 'label',
            path: 'data',
          },
        },
        {
          type: 'text',
          name: 'deviceKey',
          title: 'เลข IMEI',
          hideNumber: true,
          isRequired: true,
          requiredErrorText: 'กรุณากรอกเลข IMEI',
          placeholder: 'กรุณากรอกหมายเลข IMEI',
          startWithNewLine: true,
        },
        {
          type: 'text',
          name: 'deviceKey2',
          title: 'เลข IMEI 2',
          hideNumber: true,
          isRequired: false,
          requiredErrorText: 'กรุณากรอกเลข IMEI',
          placeholder: 'กรุณากรอกหมายเลข IMEI',
          startWithNewLine: false,
        },
        {
          type: 'text',
          name: 'phoneNumber',
          title: 'เบอร์โทรศัพท์',
          hideNumber: true,
          isRequired: true,
          placeholder: 'กรุณากรอกเบอร์โทรศัพท์',
          requiredErrorText: 'กรุณากรอกเบอร์โทรศัพท์',
          startWithNewLine: false,
        },
      ],
    },
  ],
  questionDescriptionLocation: 'underInput',
  showQuestionNumbers: 'off',
  showCompletedPage: false,
  showNavigationButtons: false,
  textUpdateMode: 'onTyping',
})
