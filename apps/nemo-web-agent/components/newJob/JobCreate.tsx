import { FC, ReactNode, useCallback, useEffect, useMemo, useState } from 'react'
import { LayoutPage } from '@/components/layout'
import { CreateFooter } from './CreateFooter'
import { useTranslation } from '@/hooks'
import {
  Button,
  GroupSection,
  StepperWithContent,
  SurveyViewDetail,
  Template,
  IQuestionAnswer,
  QuestionSurvey,
  QuestionSurveySection,
  IMediaObj,
} from 'ui'
import { stepperIcons } from '@/constants/jobs'
import { IModalCreateJobType, useJobStore } from '@/stores/jobStore'
import { useShallow } from 'zustand/react/shallow'
import { Survey } from 'survey-react-ui'
import { newDeviceDetailSurvey } from '@/survey-forms/create-job/newDeviceDetailSection'
import { theme } from '@/survey-forms'
import { Model, ChoicesRestfull, GetQuestionTitleActionsEvent, Serializer } from 'survey-core'
import { getFirebaseToken, getInitialHeader } from '@/services/auth'
import { isEqual } from 'lodash'
import {
  EstimatePrice,
  PrefillEmptyField,
  defaultValue,
  generateEncodedKeys,
  jobEstimatePrice,
  scrollToFirstError,
} from '@/utils'
import { isImageValid } from '@/utils/survey/validator'
import { getDeviceType } from '@/utils/webview'
import { useSystemStore } from '@/stores/systemStore'
import { useRouter } from 'next/router'
import { replaceHost } from 'utils'

interface Props {
  jobId: string
  eaId: string
}

export enum CustomSection {
  REMOBIE_CHECK_LIST = 'remobie_check_list',
  PRODUCT_INFORMATION = 'product_information',
}

export enum OptionChoiceMapPenalty {
  SELECT = 'select',
  NOT_SELECT = 'not_select',
  SKIP = 'skip',
}

const mergeSurvey = async (survey: Model, receivedData: object, maxTime = 4) => {
  for (let i = 0; i < maxTime; i++) {
    survey.mergeData(receivedData)
    if (isEqual(survey.getAllValues(), receivedData)) {
      break
    }
  }
}

const answerToCal = (questionAnswer: IQuestionAnswer | undefined) => {
  const adjustedAnswer: any = {}
  if (!questionAnswer) {
    return {}
  }
  Object.keys(questionAnswer).forEach(key => {
    if (!questionAnswer[key].answer) {
      adjustedAnswer[key] = OptionChoiceMapPenalty.SKIP
    } else {
      if (typeof questionAnswer[key].answer === 'string') {
        adjustedAnswer[key] = questionAnswer[key].answer
      } else {
        if (questionAnswer[key].answer.length > 0) {
          adjustedAnswer[key] = OptionChoiceMapPenalty.SELECT
        } else {
          if (questionAnswer[key].isValid) {
            adjustedAnswer[key] = OptionChoiceMapPenalty.NOT_SELECT
          } else {
            adjustedAnswer[key] = OptionChoiceMapPenalty.SKIP
          }
        }
      }
    }
  })

  return adjustedAnswer
}

const addTooltip = (_: Model, options: GetQuestionTitleActionsEvent) => {
  if (options.question.tooltip) {
    options.titleActions = [
      {
        title: 'i',
        tooltip: JSON.stringify({
          title: options.question.tooltip.title,
          html: options.question.tooltip.html,
        }),
        action: () => {},
      },
    ]
  }
}

const initSurveyAuth = async (): Promise<void> => {
  const token = await getFirebaseToken()
  ChoicesRestfull.clearCache()
  ChoicesRestfull.onBeforeSendRequest = (_, options) => {
    const headers = getInitialHeader()
    options.request.setRequestHeader('Authorization', 'Bearer ' + token)
    options.request.setRequestHeader('x-branch', headers['x-branch'])
    options.request.setRequestHeader('x-company', headers['x-company'])
    options.request.setRequestHeader('Content-Type', 'application/json')
  }
}

Serializer.addProperty('question', 'tooltip:obj')
Serializer.addProperty('question', 'previewSize:obj')
Serializer.addProperty('question', 'backgroundImage:obj')

export const JobCreate: FC<Props> = ({ jobId, eaId }) => {
  const { t } = useTranslation('common')
  const { currentLang, showCommonModal, hideCommonModal, setPopupClueModalConfig } = useSystemStore()
  const { replace, push } = useRouter()
  const {
    fetchJobById,
    job,
    uploadJobImages,
    fetchJobsHistory,
    createJob,
    modalCreateJobOpenType,
    clearModalOpenType,
    clearCreateJobModalOpenType,
    getPresignAndUpload,
  } = useJobStore(
    useShallow(state => {
      return {
        fetchJobById: state.fetchJobById,
        job: state.job,
        uploadJobImages: state.uploadJobImages,
        fetchJobsHistory: state.fetchJobsHistory,
        createJob: state.newCreateJob,
        modalCreateJobOpenType: state.modalCreateJobOpenType,
        clearModalOpenType: state.clearCreateJobModalOpenType,
        clearCreateJobModalOpenType: state.clearCreateJobModalOpenType,
        getPresignAndUpload: state.getPresignAndUpload,
      }
    })
  )

  const [price, setPrice] = useState<number>(0)
  const [lastestJobHistoryId, setLastestJobHistoryId] = useState<string>('')
  const [isMounted, setIsMounted] = useState<boolean>(false)
  const [showError, setShowError] = useState<boolean>(false)
  const [isChoiceMount, setIsChoiceMount] = useState<boolean>(false)
  const [activeStep, onStepChange] = useState<number>(0)
  const [newDeviceDetail, setNewDeviceDetail] = useState<Model>(new Model())
  const [questionAnswer, setQuestionAnswer] = useState<IQuestionAnswer>()
  const [questionImages, setQuestionImages] = useState<IMediaObj>({})

  const onErrorRedirect = useCallback(() => {
    push('/jobs')
  }, [])

  const initSurvey = (surveyTemplate: any, jobId: string): Model => {
    const host = process.env.NODE_ENV === 'development' ? 'ww-remobile-dev.axonstech.com' : window.location.host
    const modifiedTemplate = replaceHost(surveyTemplate, host)
    const qualifiedTemplate = PrefillEmptyField(modifiedTemplate)
    const surveyModel = new Model(qualifiedTemplate.survey_form)
    surveyModel.setVariable('host', host)
    surveyModel.setVariable('deviceSize', getDeviceType())
    surveyModel.applyTheme(theme)
    surveyModel.showNavigationButtons = false
    surveyModel.hideRequiredErrors = true
    surveyModel.onGetQuestionTitleActions.add(addTooltip)
    surveyModel.onAfterRenderQuestion.add(function (sender, options) {
      if (options.question.toJSON().backgroundImage) {
        const el = options.htmlElement.getElementsByClassName('sd-file__decorator') as HTMLCollectionOf<HTMLElement>
        if (el.length) {
          el[0].style.backgroundImage = `url("${options.question.toJSON().backgroundImage}")`
          el[0].style.backgroundSize = 'cover'
        }
      }
      if (options.question.name === 'emptyField') {
        options.htmlElement.style.opacity = '0'
        options.htmlElement.style.height = '0px'
      }
    })
    surveyModel.onUploadFiles.add(async (sender: Model, options) => {
      if (!options.files.length) return
      console.log('onUploadFiles')

      // Validate image file type
      options.files.forEach(file => {
        if (!isImageValid(file.name)) {
          options.callback('error')
        }
      })

      const previewUrls = await uploadJobImages(jobId, options.name, options.files)
      if (previewUrls.length) {
        options.callback(
          'success',
          options.files.map((file, i) => {
            return {
              file: {
                ...file,
                name: `${file.name}-${new Date().getTime().toString()}`,
              },
              content: previewUrls[i],
            }
          })
        )
      } else {
        options.callback('error')
      }
    })
    return surveyModel
  }

  const onCloseCommonModal = useCallback((changePath: string | null) => {
    hideCommonModal()
    clearModalOpenType()
    if (changePath !== null) {
      push(changePath)
    }
  }, [])

  useEffect(() => {
    if (
      modalCreateJobOpenType === IModalCreateJobType.unavailableEAError ||
      modalCreateJobOpenType === IModalCreateJobType.imeiNotSoldError
    ) {
      let changePath: string | null = null
      if (modalCreateJobOpenType === IModalCreateJobType.unavailableEAError) {
        changePath = '/jobs'
      }
      showCommonModal({
        icon: 'ic-warning-big-yellow',
        iconCls: '!h-16 !w-16',
        buttonType: 'single',
        ...(modalCreateJobOpenType === IModalCreateJobType.imeiNotSoldError
          ? {
              title: t('jobs-management.modal-error-imei-create-title'),
              description: t('jobs-management.modal-error-imei-create-description'),
            }
          : {
              title: t('scan-qr-code.modal-error-unavailable-title'),
              description: t('scan-qr-code.modal-error-unavailable-description-create'),
            }),
        onClose: () => onCloseCommonModal(changePath),
        onClickPositiveButton: () => onCloseCommonModal(changePath),
        positiveButtonTxt: t('ok'),
      })
    }
  }, [modalCreateJobOpenType])

  useEffect(() => {
    clearCreateJobModalOpenType()
    if (!jobId || !eaId) {
      onErrorRedirect()
    }
    fetchJobById(jobId)
      .then(job => {
        if (!job) {
          onErrorRedirect()
        }
        initSurveyAuth().then(() => {
          const deviceDetail = new Model(
            newDeviceDetailSurvey(
              currentLang,
              new URLSearchParams({ modelKey: job?.modelKey } as Record<string, string>)
            )
          )
          deviceDetail.onLoadChoicesFromServer.add((sender, _) => {
            if (!isChoiceMount) {
              setIsChoiceMount(true)
            }
          })
          deviceDetail.applyTheme(theme)
          setNewDeviceDetail(deviceDetail)
        })
      })
      .finally(() => {
        setIsMounted(true)
      })
  }, [])

  useEffect(() => {
    if (surveys?.length) {
      calPrice()
    }
  }, [questionAnswer, newDeviceDetail?.data, job?.checkListValues])

  useEffect(() => {
    if (isChoiceMount) {
      mergeSurvey(newDeviceDetail, {
        brand: job?.modelIdentifiers.brand,
        model: job?.modelIdentifiers.model,
        rom: job?.modelIdentifiers.rom,
        deviceKey: job?.deviceKey,
        deviceKey2: job?.deviceKey2,
      })
    }
    if (job?.deviceKey?.length && job?.deviceKey?.length === 15) {
      onCheckHistoryWithImei(job?.deviceKey)
    }
  }, [isChoiceMount])

  const gradeDetail: EstimatePrice = useMemo(() => {
    return jobEstimatePrice(job?.modelTemplate.modelMasterGrades ?? [], price)
  }, [price])

  const surveys = useMemo(() => {
    return job?.checkList.map(section => {
      if (!Object.values(CustomSection).includes(section.slug as CustomSection)) {
        return { ...section, model: job ? initSurvey(section, job.jobId) : new Model() }
      }
      return { ...section, model: null }
    })
  }, [job?.checkList])

  const calPrice = () => {
    const calData: any = {}
    surveys?.forEach(survey => {
      switch (survey.slug) {
        case CustomSection.REMOBIE_CHECK_LIST:
          calData[CustomSection.REMOBIE_CHECK_LIST] = job?.checkListValues[CustomSection.REMOBIE_CHECK_LIST]
          break
        case CustomSection.PRODUCT_INFORMATION:
          calData[CustomSection.PRODUCT_INFORMATION] = answerToCal(questionAnswer)
          break
        default:
          calData[survey.slug] = survey.model?.data || {}
          break
      }
    })
    const surveyKeys = generateEncodedKeys(calData)
    const penalties: any = job?.penalties || {}
    let deductPrice: number = 0
    for (const key of Array.from(surveyKeys)) {
      deductPrice += Number(defaultValue(penalties[key], '0'))
    }

    const price: number = (job?.getMaxPrice() ?? 0) + deductPrice
    setPrice(price)
  }

  const renderEachSection = (
    // render each item of checklist
    section: { slug: string; [key: string]: any }, // item in check list
    sectionValues?: any // for set value of checklist
  ): ReactNode => {
    switch (section.slug) {
      case CustomSection.REMOBIE_CHECK_LIST:
        return (
          <div key={section.slug} className={`mt-4 ${section.slug}`}>
            <SurveyViewDetail template={section as Template} answer={sectionValues} />
          </div>
        )
      case CustomSection.PRODUCT_INFORMATION:
        return (
          <GroupSection key={section.slug} isRounded={true} title={section.title} cls={`mt-4 ${section.slug}`}>
            <QuestionSurvey
              survey={section as QuestionSurveySection}
              initValue={sectionValues}
              t={t}
              currentLang={currentLang}
              onChange={val => setQuestionAnswer(val)}
              onChangeImage={mediaObj => setQuestionImages(mediaObj)}
              failAfterSubmit={showError}
              clearFailAfterSubmit={() => setShowError(false)}
              editableVDO={true}
              setPopupClueModalConfig={config => setPopupClueModalConfig(config)}
            />
          </GroupSection>
        )
      default:
        return (
          <GroupSection key={section.slug} isRounded={true} title={section.title} cls={`mt-4 ${section.slug}`}>
            <div className="mt-4">
              <Survey model={section.model} />
            </div>
          </GroupSection>
        )
    }
  }

  const onCheckHistoryWithImei = async (imei: string) => {
    const imeiData = newDeviceDetail.data
    // Fetch Job list with imei(deviceKey) to check history
    if (imeiData.deviceKey) {
      const jobResult = await fetchJobsHistory(imeiData.deviceKey)
      if (jobResult?.length) setLastestJobHistoryId(jobResult[0].jobId)
      else {
        setLastestJobHistoryId('')
      }
    }
  }

  const onTxnImeiSurveyValueChanged = async (sender: Model, options: any) => {
    if (options.name === 'deviceKey') {
      // Remove non-numeric characters
      const imei = options.value.replace(/\D/g, '').slice(0, 15)
      options.value = imei

      // Update the survey data with the cleaned value
      sender.setValue(options.name, options.value)

      if (imei && imei.length === 15) {
        onCheckHistoryWithImei(imei)
      } else {
        setLastestJobHistoryId('')
      }
    }

    if (options.name === 'deviceKey2') {
      // Remove non-numeric characters
      const imei2 = options.value.replace(/\D/g, '').slice(0, 15)
      options.value = imei2

      // Update the survey data with the cleaned value
      sender.setValue(options.name, options.value)
    }

    if (options.name === 'phoneNumber') {
      // Remove non-numeric characters
      const phoneNumber = options.value.replace(/\D/g, '')

      // No maximum limit, but minimum 10 digits required
      options.value = phoneNumber

      // Update the survey data with the cleaned value
      sender.setValue(options.name, options.value)
    }
  }

  const historyButton = () => {
    return (
      <Button
        colorScheme="primary"
        variant="outlined"
        onClick={() => push({ pathname: '/jobs/detail', query: { id: lastestJobHistoryId, view: 'history' } })}
        leftIcon={<div className="mr-2 icons ic-clock-blue" />}
      >
        {t('common.history-check')}
      </Button>
    )
  }

  const isValid = (): boolean => {
    let isValid: boolean = true
    let firstError: string = ''
    const deviceKey2Question = newDeviceDetail.getQuestionByName('deviceKey2')
    const deviceKeyQuestion = newDeviceDetail.getQuestionByName('deviceKey')
    const phoneNumberQuestion = newDeviceDetail.getQuestionByName('phoneNumber')

    if (!newDeviceDetail.validate()) {
      isValid = false
      firstError = 'device_detail'
    }
    if (deviceKey2Question.value && deviceKey2Question.value.length < 15) {
      isValid = false
      deviceKey2Question.addError('1')
      firstError = 'device_detail'
    }
    if (deviceKeyQuestion.value && deviceKeyQuestion.value.length < 15) {
      isValid = false
      deviceKeyQuestion.addError('1')
      firstError = 'device_detail'
    }
    if (
      phoneNumberQuestion.value &&
      (phoneNumberQuestion.value.length < 10 || !phoneNumberQuestion.value.startsWith('0'))
    ) {
      isValid = false
      phoneNumberQuestion.addError('1')
      firstError = 'device_detail'
    }

    surveys?.forEach(survey => {
      let valid: boolean = true
      switch (survey.slug) {
        case CustomSection.REMOBIE_CHECK_LIST:
          break
        case CustomSection.PRODUCT_INFORMATION:
          valid = Object.values(questionAnswer as IQuestionAnswer)
            .map(ans => ans.isValid)
            .every(bool => bool)
          break
        default:
          valid = Boolean(survey.model?.validate())
          break
      }
      if (valid === false) {
        setShowError(true)
        if (!firstError) {
          firstError = survey.slug
          isValid = false
        }
      }
    })
    if (isValid === false) {
      scrollToFirstError(`[class*="${firstError}"]`)
    }
    return isValid
  }

  const submit = async () => {
    if (!isValid()) {
      return
    }
    const submitData: any = {
      estimationId: eaId,
      deviceKey: newDeviceDetail.data.deviceKey,
      deviceKey2: newDeviceDetail.data.deviceKey2 ? newDeviceDetail.data.deviceKey2 : null,
      phoneNumber: newDeviceDetail.data.phoneNumber,
      answers: {} as any,
    }
    surveys?.forEach(survey => {
      switch (survey.slug) {
        case CustomSection.REMOBIE_CHECK_LIST:
          break
        case CustomSection.PRODUCT_INFORMATION:
          const adjustedAnswer: any = {}
          if (questionAnswer) {
            Object.keys(questionAnswer).forEach(key => {
              adjustedAnswer[key] = questionAnswer[key].answer
            })
          }
          submitData.answers[survey.slug] = adjustedAnswer
          break
        default:
          submitData.answers[survey.slug] = survey.model?.data || {}
          break
      }
    })

    const uploadPresign = await getPresignAndUpload(jobId, questionImages)
    submitData.answers['media'] = { product_information: uploadPresign }

    const job = await createJob(jobId, submitData)
    if (job) replace({ pathname: '/jobs/detail', query: { id: job, view: 'process' } })
  }

  return (
    <LayoutPage
      pageTitle={{
        title: t('common.jobs-management.create-job'),
        breadCrumbSuffix: lastestJobHistoryId ? historyButton() : undefined,
      }}
      subFooter={
        isMounted && (
          <CreateFooter
            price={price}
            gradeDetail={gradeDetail}
            submitButton={
              <Button
                isDisabled={price <= 0}
                colorScheme="primary"
                variant="filled"
                size="m"
                cls={`h-[48px] w-auto`}
                onClick={() => submit()}
              >
                {t('jobs-management.estimate-price')}
              </Button>
            }
          />
        )
      }
    >
      {isMounted && (
        <>
          <StepperWithContent
            steps={stepperIcons(t)}
            value={activeStep}
            onChange={onStepChange}
            cls="!min-h-[72px] !py-0 my-4"
          />
          {/* hard code part */}
          <GroupSection isRounded={true} title={t('jobs-management.device-detail')} cls={`mt-4 device_detail`}>
            <div className="mt-4">
              <Survey model={newDeviceDetail} onValueChanged={onTxnImeiSurveyValueChanged} />
            </div>
          </GroupSection>
          {surveys?.length && (
            <div>
              {surveys.map(section => {
                return renderEachSection(section, job?.checkListValues[section.slug])
              })}
            </div>
          )}
        </>
      )}
    </LayoutPage>
  )
}
